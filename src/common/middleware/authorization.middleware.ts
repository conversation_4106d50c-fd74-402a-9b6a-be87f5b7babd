import { Injectable, Logger, NestMiddleware } from "@nestjs/common";
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class AuthorizationMiddleware implements NestMiddleware {

      constructor(private readonly logger: Logger) {}

      async use(req: Request, res: Response, next: NextFunction) {
        this.logger.log(`AuthorizationMiddleware: ${req.baseUrl}, Method: ${req.method}`);
        const permissions = req['permissions'] as string[];
        
        // this to skip checking other routes for now should be updated in future
        if(!req.baseUrl.includes('/api/sensors')) {
            next();
            return;
        }
        if(req.baseUrl.includes( '/api/sensors') && req.method === 'GET' && permissions.some(x => x==='read:sensors')){
          next();
          return;
        }
        if(req.baseUrl.includes('/api/sensors') && req.method === 'POST' && permissions.some(x => x==='assign:sensors')){
            next();
          }
        else {
            res.status(403).json({ message: 'Forbidden' });
        }
      }
 }
