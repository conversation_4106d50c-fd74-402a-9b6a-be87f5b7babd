import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SensorController } from './sensor.controller';
import { SensorService } from './sensor.service';
import SensorSchema from './sensor.schema';
import Constants from 'src/common/constants';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.sensorProfile, schema: SensorSchema }]),
  ],
  controllers: [SensorController],
  providers: [SensorService],
  exports: [SensorService],
})
export class SensorModule {}
