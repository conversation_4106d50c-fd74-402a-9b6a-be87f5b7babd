import { IsString, <PERSON>N<PERSON><PERSON>, <PERSON>Optional, IsDate, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Types } from 'mongoose';

export class UpdateAssignmentDto {
  @IsOptional()
  @IsString()
  org_id?: string | null;

  @IsOptional()
  @IsString()
  auth0_id?: string | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  assigned_at?: Date | null;

  @IsOptional()
  @IsString()
  notes?: string | null;
  
  @IsOptional()
  @IsString()
  name?: string | null;
}

export class UpdateConnectivityDto {
  @IsOptional()
  @IsNumber()
  state?: number;

  @IsOptional()
  @IsNumber()
  committed_state?: number;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_change?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_online?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_offline?: Date | null;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_error?: Date | null;
}

export class UpdateLocationDto {
  @IsOptional()
  @IsString()
  last_h3?: string | null;

  @IsOptional()
  @IsNumber()
  h3_level?: number | null;

  @IsOptional()
  @IsNumber()
  lat?: number;

  @IsOptional()
  @IsNumber()
  lon?: number;

  @IsOptional()
  @IsArray()
  service_ids?: Types.ObjectId[];

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  updated_at?: Date;
}

export class UpdateRegistrationDto {
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  first_registered?: Date;
}

export class UpdateRuntimeDto {
  @IsOptional()
  @IsString()
  ip?: string | null;

  @IsOptional()
  @IsString()
  hostname?: string | null;

  @IsOptional()
  @IsString()
  code_version?: string | null;

  @IsOptional()
  @IsString()
  token_env?: string | null;
}

export class UpdateTelemetryDto {
  @IsOptional()
  @IsNumber()
  cpu?: number;

  @IsOptional()
  @IsNumber()
  mem?: number;

  @IsOptional()
  @IsNumber()
  temp?: number;

  @IsOptional()
  @IsNumber()
  storage_usage?: number;
}

export class UpdateSensorDto {
  @IsOptional()
  @IsString()
  NODE_ID?: string;

  @IsOptional()
  @IsString()
  HOST_NAME?: string;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  TIME_STAMP?: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateAssignmentDto)
  assignment?: UpdateAssignmentDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateConnectivityDto)
  connectivity?: UpdateConnectivityDto;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  first_seen?: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateLocationDto)
  location?: UpdateLocationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateRegistrationDto)
  registration?: UpdateRegistrationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateRuntimeDto)
  runtime?: UpdateRuntimeDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateTelemetryDto)
  telemetry?: UpdateTelemetryDto;
}
