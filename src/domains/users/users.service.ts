import { Injectable, NotFoundException, Inject, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { User } from './user.schema';
import { AlertZoneService } from '../alertZones/alertZone.service';
import { CacheKeyPatterns } from '../../utils/cache.utils';
import axios from 'axios';
import { OrganizationService } from '../organizations/organization.service';
import { Auth0HelperService } from 'src/utils/auth0Helper.serivce';

@Injectable()
export class UsersService {
    private readonly logger = new Logger(UsersService.name);
  
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<User>,
    private readonly alertZoneService: AlertZoneService,
    private readonly organizationService: OrganizationService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly auth0HelperService: Auth0HelperService,
  ) {}

  async findAll(filter: any): Promise<User[]> {
    return this.userModel.find(filter).exec();
  }

  async findOne(find: any): Promise<User> {
    // Cache user lookups by 'sub' (most common in authentication)
    if (find.sub && process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(find.sub);

      // Try to get from cache first
      const cachedUser = process.env.ENABLE_CACHE === 'true'? await this.cacheManager.get<User>(cacheKey) : undefined;
      if (cachedUser && process.env.ENABLE_CACHE === 'true') {
        return cachedUser;
      }

      // If not in cache, query database
      const user = await this.userModel.findOne(find).exec();

      // Store in cache with 5 minute TTL (300 seconds)
      if (user && process.env.ENABLE_CACHE === 'true') {
        await this.cacheManager.set(cacheKey, user, 300000); // 5 minutes in milliseconds
      }

      return user;
    }

    const user = await this.userModel.findOne(find)

    // For other queries, don't cache
    return user? user.toJSON() as unknown as User : null;
  }

  async findOrCreateUser(auth0Info: any): Promise<any> {
    const {
      sub,
      given_name,
      family_name,
      nickname,
      name,
      picture,
      updated_at,
      created_at,
      email,
      email_verified,
      service_zones,
      org_id,
      orgs,
      phone_number,
      phone_verified,
      roles,
      last_activity,
      org_name
    } = auth0Info;

    const user = await this.userModel
      .findOneAndUpdate(
        { sub },
        {
          sub,
          given_name,
          family_name,
          nickname,
          name,
          picture,
          updated_at,
          created_at,
          email,
          email_verified,
          service_zones,
          org_id,
          orgs,
          phone_number,
          phone_verified,
          roles,
          last_activity,
        },
        { new: true, upsert: true },
      )
      .exec();

    // Invalidate cache for this user
    if(process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(sub);
      await this.cacheManager.del(cacheKey);
    }

    const userAlertZone = this.alertZoneService.findZonesForUser(user['_id'].toString());
    const orgAlertZone = this.alertZoneService.findZonesForOrganization(user['org_id']);

    const [userAlertZoneF, orgAlertZoneF] = await Promise.all([userAlertZone, orgAlertZone]);

    user['alertZones'] = userAlertZone;
    user['org_name'] = org_name;
    return { ...user['_doc'], alertZones: userAlertZoneF, orgAlertZones: orgAlertZoneF };
  }

  async assignServiceZone(userId: String, assignServiceZoneReq: any) {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Add the service zone if it doesn't already exist
    if (!user.service_zones.includes(assignServiceZoneReq['serviceZoneIds'])) {
      assignServiceZoneReq['serviceZoneIds'].map((serviceZoneId) => {
        const zoneObjectId = new Types.ObjectId(serviceZoneId);
        user.service_zones.push(zoneObjectId);
      });
    }

    const savedUser = await user.save();

    // Invalidate cache for this user
    if (user.sub && process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(user.sub);
      await this.cacheManager.del(cacheKey);
    }

    return savedUser;
  }

  async removeUserBySub(sub: string) {
    const query = { sub };
    const result = await this.userModel.deleteOne(query);

    // Invalidate cache for this user
    if(process.env.ENABLE_CACHE === 'true') {
      const cacheKey = CacheKeyPatterns.USER_BY_SUB(sub);
      await this.cacheManager.del(cacheKey);
    }

    if (result.deletedCount === 1) {
      console.log("Successfully deleted one document.");
    } else {
      console.log("No documents matched the query. Deleted 0 documents.");
    }
    return result;
  }

  async syncUserWithAuth0(auth0Info: any) : Promise<User>{
    const ManagementToken = await this.auth0HelperService.getManagementTokenResponse();

    const userInfoPromise = axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${auth0Info.data.sub}`, {
      headers: {
        Authorization: `Bearer ${ManagementToken.access_token}`,
      },
    });

    const userOrgsPromise = axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${auth0Info.data.sub}/organizations`, {
      headers: {
        Authorization: `Bearer ${ManagementToken.access_token}`,
      },
    });

    const userRolesPromise = axios.get(
      `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${auth0Info.data.org_id}/members/${auth0Info.data.sub}/roles`,
      {
        headers: {
          Authorization: `Bearer ${ManagementToken.access_token}`,
          maxBodyLength: Infinity,
        },
      },
    );

    const [userInfo, userOrgs, userRoles] = await Promise.all([userInfoPromise, userOrgsPromise, userRolesPromise]);


    if (!userInfo.data['family_name']) {
      const firstPartOfEmail = (auth0Info.data.email as string).split('@')[0];
      const updatedResultInformationResult = this.updateUserInformationPromise({
        given_name: firstPartOfEmail,
        family_name: firstPartOfEmail,
        username: firstPartOfEmail,
        name: firstPartOfEmail + " " + "‎"
      }, auth0Info.data.sub, ManagementToken.access_token).then((result) => {
        return result.data
      }).catch((e) => {
        console.log(e.response.data);
      });
    }


    const orgInfo = userOrgs.data.find((org) => org.id === auth0Info.data.org_id);

    const user = auth0Info.data;

    user['phone_number'] = userInfo.data['phone_number'];
    user['phone_verified'] = userInfo.data['phone_verified'];
    user['roles'] = userRoles.data;
    user['last_activity'] = new Date().toISOString();
    user['created_at'] = userInfo.data['created_at']
    user['org_name'] = orgInfo.display_name;

    user['orgs'] = userOrgs.data.map((org) => org.id);

    const org = await this.organizationService.createOrUpdateByAuth0Id(orgInfo.id, orgInfo);

    const finalUser = await this.findOrCreateUser(user);

    return finalUser;
  }


  async updateUserInformationPromise(userDto, userSub: string, access_token: string) {
    let data = JSON.stringify({
      phone_number: userDto.phone_number,
      given_name: userDto.given_name,
      family_name: userDto.family_name,
      name: userDto.name,
      nickname: userDto.nickname,
      // picture: userDto.picture,
      client_id: process.env.AUTH0_CLIENT_ID,
    });

    let config = {
      method: 'patch',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${userSub}`,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
      data: data,
    };
    return axios.request(config);
  }

}