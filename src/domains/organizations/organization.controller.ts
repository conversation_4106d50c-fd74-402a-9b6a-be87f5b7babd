import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpException,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe
} from '@nestjs/common';
import { OrganizationService } from './organization.service';
import { Organizations } from './organization.interface';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

@Controller('api/organizations')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  async findAll(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('pageSize', new DefaultValuePipe(10), ParseIntPipe) pageSize: number,
    @Query() filters: any
  ): Promise<{ organizations: Organizations[], total: number, page: number, totalPages: number }> {
    try {
      // Remove pagination params from filters
      const { page: _, pageSize: __, ...filterParams } = filters;
      console.log({ filterParams })
      const skip = (page - 1) * pageSize;
      const organizations = await this.organizationService.findAll(filterParams);
      
      // Apply pagination manually since service doesn't support it yet
      const paginatedOrgs = organizations.slice(skip, skip + pageSize);
      const total = organizations.length;
      const totalPages = Math.ceil(total / pageSize);

      return {
        organizations: paginatedOrgs,
        total,
        page,
        totalPages
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to fetch organizations',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('by-auth0-id/:auth0Id')
  async findByAuth0Id(@Param('auth0Id') auth0Id: string): Promise<Organizations> {
    try {
      const organization = await this.organizationService.findByAuth0Id(auth0Id);
      if (!organization) {
        throw new HttpException(
          {
            success: false,
            message: `Organization with auth0_id ${auth0Id} not found`
          },
          HttpStatus.NOT_FOUND
        );
      }
      return organization;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to fetch organization',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get("/sync-with-auth0")
  async syncOrgsWithAuth0() {
    return this.organizationService.syncOrgsWithAuth0();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Organizations> {
    try {
      const organization = await this.organizationService.findById(id);
      if (!organization) {
        throw new HttpException(
          {
            success: false,
            message: `Organization with ID ${id} not found`
          },
          HttpStatus.NOT_FOUND
        );
      }
      return organization;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to fetch organization',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post()
  async create(
    @Body() organizationData: CreateOrganizationDto
  ): Promise<Organizations> {
    try {
      return await this.organizationService.create(organizationData);
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to create organization',
          error: error.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() organizationData: UpdateOrganizationDto
  ): Promise<Organizations> {
    try {
      const updatedOrganization = await this.organizationService.update(id, organizationData);
      
      if (!updatedOrganization) {
        throw new HttpException(
          {
            success: false,
            message: `Organization with ID ${id} not found`
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return updatedOrganization;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to update organization',
          error: error.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Put('by-auth0-id/:auth0Id')
  async createOrUpdateByAuth0Id(
    @Param('auth0Id') auth0Id: string,
    @Body() organizationData: UpdateOrganizationDto
  ): Promise<Organizations> {
    try {
      return await this.organizationService.createOrUpdateByAuth0Id(auth0Id, organizationData);
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          message: 'Failed to create or update organization',
          error: error.message
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(':id')
  async delete(
    @Param('id') id: string
  ): Promise<{ message: string }> {
    try {
      const deleted = await this.organizationService.delete(id);
      
      if (!deleted) {
        throw new HttpException(
          {
            success: false,
            message: `Organization with ID ${id} not found`
          },
          HttpStatus.NOT_FOUND
        );
      }
      
      return { message: 'Organization deleted successfully' };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        {
          success: false,
          message: 'Failed to delete organization',
          error: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


}
