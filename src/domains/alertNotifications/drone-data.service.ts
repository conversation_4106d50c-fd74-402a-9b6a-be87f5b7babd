import { NotificationTypeEnum } from "src/common/enums/NotifocationTypeEnum";

const OpenDroneID_basicID_uaType = Object.freeze({
    0: 'None/Not Declared',
    1: 'Aeroplane',
    2: 'Helicopter/Multirotor',
    3: 'Gryroplane',
    4: 'Hybrid Lift (Fixed wing that can take off vertically)',
    5: 'Ornithopter',
    6: 'Glider',
    7: 'Kit<PERSON>',
    8: 'Free Balloon',
    9: 'Captive Balloon',
    10: 'Airship (Such as a blimp)',
    11: 'Free Fall/Parachute (unpowered)',
    12: 'Rocket',
    13: 'Tethered Powered Aircraft',
    14: 'Ground Obstacles',
    15: 'Other'
});

const OpenDroneID_loc_status = Object.freeze({
    0: 'Undeclared',
    1: 'Ground',
    2: 'Airborne',
    3: 'Emergency',
    4: 'Remote ID System Failure',
    5: 'Reserved',
    6: 'Reserved',
    7: 'Reserved',
    8: 'Reserved',
    9: 'Reserved',
    10: 'Reserved',
    11: 'Reserved',
    12: 'Reserved',
    13: 'Reserved',
    14: 'Reserved',
    15: 'Reserved'
});

const OpenDroneID_operator_type = Object.freeze({
    0: 'Takeoff',
    1: 'Dynamic',
    2: 'Fixed'
});

function prepareDroneDetectionData(event, alertZone, droneAuthId = null, type: NotificationTypeEnum = NotificationTypeEnum.ALERT) {
    const uasType = OpenDroneID_basicID_uaType?.[event.INFO?.INFO?.ODID_basicID_uaType] || 'N/A';
    const locationStatus = OpenDroneID_loc_status?.[event.INFO?.INFO?.ODID_loc_status] || event.INFO?.INFO?.ODID_loc_status;
    const operatorType = OpenDroneID_operator_type?.[event.INFO?.INFO?.ODID_operator_type] || 'N/A';
    const operatorLat = event.INFO?.INFO?.ODID_system_lat || 'N/A';
    const operatorLon = event.INFO?.INFO?.ODID_system_lon || 'N/A';
    const droneHeading = event.INFO?.INFO?.ODID_loc_direction || 'N/A';

    return {
        alertZoneId: alertZone._id,
        alertZoneName: alertZone.name,
        currentDate: new Date().toLocaleString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', second: '2-digit' }),
        eventId: event.EVENT_ID,
        uasId: event.UAS_ID,
        uasType,
        droneLat: event.INFO?.LAT,
        droneLng: event.INFO?.LON,
        pilotLat: operatorLat,
        pilotLng: operatorLon,
        locationStatus,
        altitude: event.INFO?.ALTITUDE,
        speed: event.INFO?.SPEED,
        heading: droneHeading,
        operatorType,
        complete: event.COMPLETE,
        event,
        droneAuthId,
        type
    };
}

export default prepareDroneDetectionData