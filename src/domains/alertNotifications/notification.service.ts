import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification } from './notification.interface';
import { ObjectId } from 'mongodb';
import { OrganizationService } from '../organizations/organization.service';
import Constants from 'src/common/constants';

@Injectable()
export class NotificationService {
  constructor(
    @InjectModel(Constants.notification)
    private readonly notificationModel: Model<Notification>,
    private readonly organizationService: OrganizationService
  ) {}

  async findAllActiveEventsByOrgId(org_id: string): Promise<Array<Notification>> {
    console.log('finding notifications for org: ');
    const realOrgId = await this.organizationService.findByAuth0Id(org_id);
    
    return this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: realOrgId._id,
          }
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'event',
          },
        },
        {
          $match: {
            'event.COMPLETE': 0
          },
        }
      ])
      .exec();
  }

  findOne(notification_id: string) {
    return this.notificationModel.findById(notification_id);
  }

  async findByEventIdAndOrgId(event_id: string, org_id: string) {
    const realOrgId = await this.organizationService.findByAuth0Id(org_id);
    return this.notificationModel.find({ event_id, org_id: realOrgId._id });
  }

  async findByMultipleEventIdAndOrgId(event_ids: string[], org_id: string) {
    const realOrgId = await this.organizationService.findByAuth0Id(org_id);
    return this.notificationModel.aggregate(
    [
      {$match: { event_id: {$in: event_ids}, org_id: realOrgId._id }}
    ]
    ).exec();
  }

  async update(id: string, notificationData: Partial<Notification>) {
    return this.notificationModel.findByIdAndUpdate(id, notificationData, { new: true });
  }
}
